import 'dart:io';
import 'dart:async';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import '../../providers/nickname_provider.dart';
import '../../widgets/profile_avatar_widget.dart';
import '../../widgets/image_crop_widget.dart';
import 'package:path_provider/path_provider.dart';
import '../../utils/logger_utils.dart';
import '../../utils/local_data_cleaner.dart';
import '../../utils/firebase_upload_utils.dart';
import '../../utils/image_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/logout_manager.dart';
import '../../widgets/account_deletion_dialog.dart';
import 'settings_screen.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';

class MyPageScreen extends ConsumerStatefulWidget {
  const MyPageScreen({super.key});

  @override
  ConsumerState<MyPageScreen> createState() => _MyPageScreenState();
}

class _MyPageScreenState extends ConsumerState<MyPageScreen> {
  bool _loadingImage = false;

  @override
  void initState() {
    super.initState();
    _loadProfileImage();
  }

  Future<void> _loadProfileImage() async {
    setState(() => _loadingImage = true);
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // 프로필 이미지 로딩 로직 (기존과 동일)
        final nickname = ref.read(nicknameProvider);
        if (nickname?.profileImagePath != null &&
            nickname!.profileImagePath!.isNotEmpty) {
          final localFile = File(nickname.profileImagePath!);
          if (await localFile.exists()) {
            LoggerUtils.logInfo('로컬 프로필 이미지 발견', tag: 'MyPageScreen');
            setState(() => _loadingImage = false);
            return;
          }
        }

        // Firebase Storage에서 다운로드
        try {
          final ref = FirebaseStorage.instance
              .ref()
              .child('users/${user.uid}/profile_image.jpg');
          await ref.getDownloadURL(); // URL 존재 확인
          LoggerUtils.logInfo('프로필 이미지 URL 가져오기 성공', tag: 'MyPageScreen');
        } catch (e) {
          LoggerUtils.logInfo('프로필 이미지 없음 또는 가져오기 실패: $e', tag: 'MyPageScreen');
        }
      }
    } catch (e) {
      LoggerUtils.logError('프로필 이미지 로딩 실패', tag: 'MyPageScreen', error: e);
    } finally {
      if (mounted) setState(() => _loadingImage = false);
    }
  }

  Future<void> _pickAndUploadProfileImage() async {
    final picker = ImagePicker();
    final picked = await picker.pickImage(source: ImageSource.gallery, imageQuality: 85);
    if (picked == null) return;
    
    // 1. 3단계 방식: 흰색 800x800 캔버스 + 이미지 최대 650px로 전처리
    final originalBytes = await picked.readAsBytes();
    final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
    final tempDir = await Directory.systemTemp.createTemp('profile_crop_temp');
    final tempFile = File('${tempDir.path}/padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
    await tempFile.writeAsBytes(paddedBytes);
    
    // 2. 크롭 다이얼로그 호출 (흰색 패딩 포함 이미지)
    final cropped = await ImageCropUtils.cropImage(
      context: context,
      imagePath: tempFile.path,
      shape: CropShape.circle,
      aspectRatio: 1.0,
    );
    if (cropped == null) return;
    
    // 3. 크롭된 이미지를 200x200으로 리사이즈만 함 (addWhitePaddingAndCenterImage 호출 X)
    final croppedBytes = await cropped.readAsBytes();
    final img.Image? imgDecoded = img.decodeImage(croppedBytes);
    final img.Image imgResized = img.copyResize(imgDecoded!, width: 200, height: 200);
    final jpgBytes = img.encodeJpg(imgResized, quality: 80);
    final finalFile = File('${tempDir.path}/final_${DateTime.now().millisecondsSinceEpoch}.jpg');
    await finalFile.writeAsBytes(jpgBytes);
    
    setState(() {
      _loadingImage = true;
    });
    
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logError(
          '로그인된 사용자가 없습니다',
          tag: 'MyPageScreen',
        );
        return;
      }

      LoggerUtils.logDebug(
        '로그인된 사용자: ${user.email}, UID: ${user.uid}',
        tag: 'MyPageScreen',
      );

      // 새로운 경로 구조: users/{userId}/profile_images/profile.jpg
      final profileImagePath = 'users/${user.uid}/profile_images/profile.jpg';
      LoggerUtils.logDebug(
        'Firebase 업로드 시작: $profileImagePath',
        tag: 'MyPageScreen',
      );

      // Firebase Storage에 업로드
      final downloadUrl = await FirebaseUploadUtils.uploadFile(
        finalFile,
        profileImagePath,
        onProgress: (progress) {
          LoggerUtils.logDebug(
            'Upload progress: ${(progress * 100).toStringAsFixed(1)}%',
            tag: 'MyPageScreen',
          );
        },
      );

      // Firestore에 URL 저장
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
        'profileImageUrl': downloadUrl,
      }, SetOptions(merge: true));

      LoggerUtils.logDebug(
        'Firebase 업로드 완료: $downloadUrl',
        tag: 'MyPageScreen',
      );

      // 로컬에 프로필 이미지 저장 (더 안전한 방식)
      final dir = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final localFile = File('${dir.path}/profile_${user.uid}_$timestamp.jpg');

      LoggerUtils.logDebug(
        '로컬 파일 저장 시도: ${localFile.path}',
        tag: 'MyPageScreen',
      );
      LoggerUtils.logDebug(
        '저장할 데이터 크기: ${jpgBytes.length} bytes',
        tag: 'MyPageScreen',
      );
      LoggerUtils.logDebug(
        '디렉토리 경로: ${dir.path}',
        tag: 'MyPageScreen',
      );

      // 파일 저장 전 디렉토리 확인 및 생성
      if (!await dir.exists()) {
        await dir.create(recursive: true);
        LoggerUtils.logDebug(
          '디렉토리 생성: ${dir.path}',
          tag: 'MyPageScreen',
        );
      }

      // 디렉토리 권한 확인
      final dirStat = await dir.stat();
      LoggerUtils.logDebug(
        '디렉토리 권한: ${dirStat.mode}',
        tag: 'MyPageScreen',
      );

      // 파일 저장 (동기화 강제)
      await localFile.writeAsBytes(jpgBytes, flush: true);

      // 파일 시스템 동기화를 위한 대기
      await Future.delayed(const Duration(milliseconds: 200));

      // 파일 존재 및 데이터 무결성 확인 (여러 번 시도)
      bool fileVerified = false;
      for (int attempt = 1; attempt <= 3; attempt++) {
        LoggerUtils.logDebug(
          '파일 검증 시도 $attempt/3',
          tag: 'MyPageScreen',
        );

        if (await localFile.exists()) {
          final fileSize = await localFile.length();
          LoggerUtils.logDebug(
            '파일 크기: $fileSize bytes (예상: ${jpgBytes.length} bytes)',
            tag: 'MyPageScreen',
          );

          if (fileSize > 0 && fileSize == jpgBytes.length) {
            // 실제 이미지 데이터 검증
            try {
              final savedBytes = await localFile.readAsBytes();
              if (savedBytes.length == jpgBytes.length) {
                LoggerUtils.logDebug(
                  '로컬 파일 저장 및 검증 완료: ${localFile.path} (크기: $fileSize bytes)',
                  tag: 'MyPageScreen',
                );
                fileVerified = true;
                break;
              } else {
                LoggerUtils.logError(
                  '저장된 파일 크기 불일치: ${savedBytes.length} != ${jpgBytes.length}',
                  tag: 'MyPageScreen',
                );
              }
            } catch (e) {
              LoggerUtils.logError(
                '저장된 파일 읽기 실패 (시도 $attempt)',
                error: e,
                tag: 'MyPageScreen',
              );
            }
          } else {
            LoggerUtils.logError(
              '파일 크기 불일치 (시도 $attempt): $fileSize != ${jpgBytes.length}',
              tag: 'MyPageScreen',
            );
          }
        } else {
          LoggerUtils.logError(
            '파일 존재하지 않음 (시도 $attempt): ${localFile.path}',
            tag: 'MyPageScreen',
          );
        }

        if (attempt < 3) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      if (!fileVerified) {
        throw Exception('로컬 파일 저장 검증 실패 (3회 시도 후)');
      }

      // 기존 프로필 이미지 파일들 정리 (최신 것만 유지)
      final files = dir
          .listSync()
          .whereType<File>()
          .where((f) => (f.path.contains('profile_image_${user.uid}_') || f.path.contains('profile_${user.uid}_')) && f.path.endsWith('.jpg'))
          .toList();
      files.sort((a, b) => b.path.compareTo(a.path));
      for (int i = 1; i < files.length; i++) {
        try { await files[i].delete(); } catch (_) {}
      }

      // NicknameProvider 상태 즉시 업데이트
      await ref.read(nicknameProvider.notifier).updateProfileImage(localFile.path, downloadUrl);

      LoggerUtils.logDebug(
        '프로필 이미지 업로드 및 로컬 저장 완료: ${localFile.path}',
        tag: 'MyPageScreen',
      );
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('프로필 이미지가 저장되었습니다.'),
        backgroundColor: Colors.green,
      ));

    } catch (e) {
      LoggerUtils.logError(
        '프로필 이미지 업로드 실패',
        error: e,
        tag: 'MyPageScreen',
      );
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('이미지 업로드 실패: $e'),
        backgroundColor: Colors.red,
      ));
    }
    setState(() => _loadingImage = false);
  }

  Future<void> _showChangePasswordDialog() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null || user.email == null) return;
    final ok = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('비밀번호 재설정'),
        content: Text('${user.email}로 비밀번호 재설정 메일을 발송할까요?'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('취소')),
          ElevatedButton(onPressed: () => Navigator.pop(context, true), child: const Text('발송')),
        ],
      ),
    );
    if (ok == true) {
      try {
        await FirebaseAuth.instance.sendPasswordResetEmail(email: user.email!);
        ToastUtils.showSuccess(context, '비밀번호 재설정 메일이 발송되었습니다. 이메일을 확인하세요.');
      } catch (e) {
        ToastUtils.showError(context, '메일 발송 실패: $e');
      }
    }
  }

  /// 새로운 로그아웃 - Phoenix를 사용한 완전한 앱 재시작
  Future<void> _logout() async {
    await LogoutManager.performCompleteLogout(
      context: context,
      ref: ref,
    );
  }

  /// 비밀번호 확인 다이얼로그
  Future<String?> _showPasswordConfirmDialog() async {
    final passwordController = TextEditingController();
    String? result;

    try {
      result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('비밀번호 확인'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('보안을 위해 현재 비밀번호를 입력해 주세요.'),
              const SizedBox(height: 16),
              TextField(
                controller: passwordController,
                obscureText: true,
                autofocus: true,
                decoration: const InputDecoration(
                  labelText: '현재 비밀번호',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.lock),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty && mounted) {
                    Navigator.of(context).pop(value);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (mounted) {
                  Navigator.of(context).pop(null);
                }
              },
              child: const Text('취소'),
            ),
            ElevatedButton(
              onPressed: () {
                final value = passwordController.text;
                if (value.isNotEmpty && mounted) {
                  Navigator.of(context).pop(value);
                }
              },
              child: const Text('확인'),
            ),
          ],
        ),
      );
    } catch (e) {
      LoggerUtils.logError('비밀번호 확인 다이얼로그 오류: $e', tag: 'MyPageScreen');
      result = null;
    }

    // 다이얼로그가 완전히 닫힌 후 안전하게 dispose
    // 다음 프레임에서 dispose하여 위젯 트리가 완전히 정리된 후 실행
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        passwordController.dispose();
      } catch (e) {
        LoggerUtils.logWarning('TextEditingController dispose 오류: $e', tag: 'MyPageScreen');
      }
    });

    return result;
  }

  /// === 개선된 회원탈퇴 함수 ===
  Future<void> _deleteAccount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    // 1. 회원탈퇴 확인 다이얼로그
    final ok = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.warning, color: Colors.red),
        title: const Text('회원탈퇴'),
        content: const Text('정말로 회원탈퇴 하시겠습니까?\n모든 데이터가 완전히 삭제됩니다.'),
        actions: [
          TextButton(
            onPressed: () {
              if (mounted) {
                Navigator.of(context).pop(false);
              }
            },
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              if (mounted) {
                Navigator.of(context).pop(true);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('탈퇴'),
          ),
        ],
      ),
    );
    if (ok != true) return;

    // 2. 재인증을 위한 비밀번호 입력
    final email = user.email;
    if (email == null) {
      if (mounted) {
        ToastUtils.showError(context, '이메일 정보를 찾을 수 없습니다.');
      }
      return;
    }

    // 3. 비밀번호 확인 다이얼로그
    final password = await _showPasswordConfirmDialog();
    if (password == null || password.isEmpty) {
      if (mounted) {
        ToastUtils.showInfo(context, '회원탈퇴가 취소되었습니다.');
      }
      return;
    }

    // 4. 회원탈퇴 전용 로딩 다이얼로그 표시
    if (!mounted) return;

    try {
      LoggerUtils.logInfo('회원탈퇴 다이얼로그 표시 시작', tag: 'MyPageScreen');
      await AccountDeletionDialogManager.show(context);
      LoggerUtils.logInfo('회원탈퇴 다이얼로그 표시 완료', tag: 'MyPageScreen');

      // 다이얼로그가 완전히 렌더링될 때까지 추가 대기
      await Future.delayed(const Duration(milliseconds: 300));
      LoggerUtils.logInfo('다이얼로그 렌더링 대기 완료', tag: 'MyPageScreen');
    } catch (e) {
      LoggerUtils.logError('회원탈퇴 다이얼로그 표시 실패: $e', tag: 'MyPageScreen');
      if (mounted) {
        ToastUtils.showError(context, '회원탈퇴 다이얼로그 표시에 실패했습니다.');
      }
      return;
    }

    try {
      // 5. 사용자 상태 재확인
      LoggerUtils.logInfo('회원탈퇴: 사용자 상태 확인', tag: 'MyPageScreen');
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null || currentUser.uid != user.uid) {
        throw Exception('사용자 세션이 만료되었습니다. 다시 로그인해 주세요.');
      }

      // 재인증 수행 (타임아웃 적용)
      LoggerUtils.logInfo('회원탈퇴: 재인증 시작', tag: 'MyPageScreen');
      LoggerUtils.logInfo('현재 사용자: ${user.uid}, 이메일: ${user.email}', tag: 'MyPageScreen');

      // 다이얼로그 업데이트 (여러 번 시도)
      for (int i = 0; i < 3; i++) {
        AccountDeletionDialogManager.updateStep(AccountDeletionStep.authenticating);
        await Future.delayed(const Duration(milliseconds: 100));
        if (AccountDeletionDialogManager.isShowing) {
          break;
        }
      }
      LoggerUtils.logInfo('다이얼로그 단계 업데이트: authenticating', tag: 'MyPageScreen');

      // 재인증 자격 증명 생성
      LoggerUtils.logInfo('재인증 자격 증명 생성 중...', tag: 'MyPageScreen');
      final cred = EmailAuthProvider.credential(email: email, password: password);

      // 재인증 실행
      LoggerUtils.logInfo('Firebase 재인증 요청 시작...', tag: 'MyPageScreen');
      try {
        await user.reauthenticateWithCredential(cred).timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            LoggerUtils.logError('재인증 시간 초과', tag: 'MyPageScreen');
            throw TimeoutException('재인증 시간 초과', const Duration(seconds: 30));
          },
        );
        LoggerUtils.logInfo('회원탈퇴: 재인증 완료', tag: 'MyPageScreen');
      } on FirebaseAuthException catch (e) {
        LoggerUtils.logError('Firebase 재인증 실패: ${e.code} - ${e.message}', tag: 'MyPageScreen');
        if (e.code == 'wrong-password') {
          throw Exception('비밀번호가 올바르지 않습니다.');
        } else if (e.code == 'user-disabled') {
          throw Exception('계정이 비활성화되었습니다.');
        } else if (e.code == 'user-not-found') {
          throw Exception('사용자를 찾을 수 없습니다.');
        } else if (e.code == 'network-request-failed') {
          throw Exception('네트워크 오류가 발생했습니다.');
        } else {
          throw Exception('재인증 실패: ${e.message}');
        }
      } catch (e) {
        LoggerUtils.logError('재인증 중 예상치 못한 오류: $e', tag: 'MyPageScreen');
        rethrow;
      }

      // 재인증 완료 후 잠시 대기
      await Future.delayed(const Duration(milliseconds: 500));

      // 6. Firebase Functions 호출 (모든 서버 데이터 + Auth 계정 삭제)
      LoggerUtils.logInfo('회원탈퇴: Functions 호출 시작', tag: 'MyPageScreen');
      AccountDeletionDialogManager.updateStep(AccountDeletionStep.deletingServerData);

      final callable = FirebaseFunctions.instance.httpsCallable('deleteUserData');
      final result = await callable.call().timeout(
        const Duration(minutes: 2),
        onTimeout: () => throw TimeoutException('서버 데이터 삭제 시간 초과', const Duration(minutes: 2)),
      );

      if (result.data['success'] == true) {
        LoggerUtils.logInfo('회원탈퇴: Functions 삭제 완료 - ${result.data['message']}', tag: 'MyPageScreen');

        // 서버 데이터 삭제 완료 후 잠시 대기
        await Future.delayed(const Duration(milliseconds: 500));

        // 7. 로컬 데이터 삭제 (타임아웃 적용)
        LoggerUtils.logInfo('회원탈퇴: 로컬 데이터 삭제 시작', tag: 'MyPageScreen');
        AccountDeletionDialogManager.updateStep(AccountDeletionStep.deletingLocalData);

        await Future.wait([
          LocalDataCleaner.clearDataForAccountDeletion(ref: ref),
          LocalDataCleaner.clearUserSpecificData(user.uid),
        ]).timeout(
          const Duration(minutes: 1),
          onTimeout: () => throw TimeoutException('로컬 데이터 삭제 시간 초과', const Duration(minutes: 1)),
        );
        LoggerUtils.logInfo('회원탈퇴: 로컬 데이터 삭제 완료', tag: 'MyPageScreen');

        // 로컬 데이터 삭제 완료 후 충분한 대기 시간 확보
        await Future.delayed(const Duration(milliseconds: 1000));

        // 8. 앱 재시작 준비
        LoggerUtils.logInfo('회원탈퇴: 앱 재시작 준비', tag: 'MyPageScreen');
        AccountDeletionDialogManager.updateStep(AccountDeletionStep.restarting);

        // 앱 재시작 전 추가 대기 시간
        await Future.delayed(const Duration(milliseconds: 1500));

        // 9. 다이얼로그 닫기 및 앱 완전 재시작
        await _safeRestartApp();

      } else {
        throw Exception('Functions 호출 실패: ${result.data}');
      }

    } catch (e) {
      LoggerUtils.logError('회원탈퇴 실패: $e', tag: 'MyPageScreen');
      await _handleAccountDeletionError(e);
    }
  }

  /// 안전한 앱 재시작
  Future<void> _safeRestartApp() async {
    try {
      LoggerUtils.logInfo('앱 재시작 시작', tag: 'MyPageScreen');

      if (!mounted) return;

      // 다이얼로그 닫기
      AccountDeletionDialogManager.dismiss(context);
      LoggerUtils.logInfo('다이얼로그 닫기 완료', tag: 'MyPageScreen');

      // 다이얼로그가 완전히 닫힐 때까지 대기
      await Future.delayed(const Duration(milliseconds: 800));

      if (!mounted) return;

      LoggerUtils.logInfo('Navigator를 사용한 앱 재시작 시도', tag: 'MyPageScreen');

      // Navigator를 사용해서 모든 스택을 클리어하고 온보딩으로 이동
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/',
        (route) => false,
      );

      LoggerUtils.logInfo('앱 재시작 완료', tag: 'MyPageScreen');

    } catch (e) {
      LoggerUtils.logError('앱 재시작 실패: $e', tag: 'MyPageScreen');

      // Navigator 방법이 실패하면 Phoenix 시도
      try {
        if (mounted) {
          LoggerUtils.logInfo('Phoenix 재시작 시도', tag: 'MyPageScreen');
          Phoenix.rebirth(context);
        }
      } catch (phoenixError) {
        LoggerUtils.logError('Phoenix 재시작도 실패: $phoenixError', tag: 'MyPageScreen');

        // 최종 안전장치: 앱 종료
        try {
          LoggerUtils.logInfo('SystemNavigator.pop() 시도', tag: 'MyPageScreen');
          SystemNavigator.pop();
        } catch (systemError) {
          LoggerUtils.logError('SystemNavigator.pop() 실패: $systemError', tag: 'MyPageScreen');

          // 정말 마지막 안전장치
          AccountDeletionDialogManager.forceCleanup();

          // 사용자에게 수동 재시작 안내
          if (mounted) {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('회원탈퇴 완료'),
                content: const Text('회원탈퇴가 완료되었습니다.\n앱을 수동으로 재시작해 주세요.'),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // 다이얼로그 닫은 후 앱 종료 재시도
                      try {
                        SystemNavigator.pop();
                      } catch (e) {
                        // 무시
                      }
                    },
                    child: const Text('확인'),
                  ),
                ],
              ),
            );
          }
        }
      }
    }
  }

  /// 회원탈퇴 오류 처리
  Future<void> _handleAccountDeletionError(dynamic error) async {
    try {
      if (!mounted) return;

      // 다이얼로그 닫기
      AccountDeletionDialogManager.dismiss(context);

      // 다이얼로그가 완전히 닫힐 때까지 대기
      await Future.delayed(const Duration(milliseconds: 300));

      if (!mounted) return;

      // 오류 메시지 결정
      String errorMessage = '회원탈퇴 중 오류가 발생했습니다.';

      if (error is FirebaseAuthException && error.code == 'wrong-password') {
        errorMessage = '비밀번호가 올바르지 않습니다.';
      } else if (error is TimeoutException) {
        errorMessage = '작업 시간이 초과되었습니다. 네트워크 상태를 확인하고 다시 시도해 주세요.';
      } else if (error.toString().contains('network')) {
        errorMessage = '네트워크 오류가 발생했습니다. 다시 시도해 주세요.';
      } else if (error.toString().contains('permission')) {
        errorMessage = '권한 오류가 발생했습니다. 다시 로그인 후 시도해 주세요.';
      }

      // 오류 다이얼로그 표시
      await showDialog<void>(
        context: context,
        builder: (context) => AlertDialog(
          icon: const Icon(Icons.error, color: Colors.red),
          title: const Text('회원탈퇴 실패'),
          content: Text(errorMessage),
          actions: [
            TextButton(
              onPressed: () {
                if (mounted) {
                  Navigator.of(context).pop();
                }
              },
              child: const Text('확인'),
            ),
          ],
        ),
      );
    } catch (e) {
      LoggerUtils.logError('오류 처리 중 추가 오류: $e', tag: 'MyPageScreen');
      // 최종 안전장치
      AccountDeletionDialogManager.forceCleanup();
    }
  }

  @override
  Widget build(BuildContext context) {
    final nickname = ref.watch(nicknameProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('마이페이지'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          children: [
            Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 업로드/크롭 중이면 기존 이미지를 아예 렌더링하지 않음
                  if (!_loadingImage)
                    ProfileAvatarWidget(
                      radius: 48,
                      nickname: nickname,
                      backgroundColor: Colors.grey[100],
                    ),
                  // 업로드/크롭 중에는 오버레이+로딩만 단독 노출
                  if (_loadingImage)
                    Container(
                      width: 96,
                      height: 96,
                      decoration: const BoxDecoration(
                        color: Colors.black38,
                        shape: BoxShape.circle,
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                  // 카메라 버튼
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: IconButton(
                      icon: const Icon(Icons.camera_alt, color: Colors.blue),
                      onPressed: _pickAndUploadProfileImage,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            ListTile(
              title: const Text('이메일'),
              subtitle: Text(FirebaseAuth.instance.currentUser?.email ?? '이메일 없음'),
              leading: const Icon(Icons.email),
            ),
            const Divider(),
            ListTile(
              title: const Text('닉네임'),
              subtitle: Text(nickname?.name ?? '닉네임 없음'),
              trailing: IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () async {
                  final controller = TextEditingController(text: nickname?.name ?? '');
                  String? result;

                  try {
                    result = await showDialog<String>(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('닉네임 수정'),
                        content: TextField(
                          controller: controller,
                          decoration: const InputDecoration(hintText: '새 닉네임 입력'),
                          autofocus: true,
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('취소'),
                          ),
                          ElevatedButton(
                            onPressed: () => Navigator.pop(context, controller.text),
                            child: const Text('저장'),
                          ),
                        ],
                      ),
                    );
                  } catch (e) {
                    LoggerUtils.logError('닉네임 수정 다이얼로그 오류: $e', tag: 'MyPageScreen');
                    result = null;
                  }

                  // 다이얼로그가 완전히 닫힌 후 안전하게 dispose
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    try {
                      controller.dispose();
                    } catch (e) {
                      LoggerUtils.logWarning('닉네임 TextEditingController dispose 오류: $e', tag: 'MyPageScreen');
                    }
                  });

                  if (result != null && result.isNotEmpty && result != nickname?.name) {
                    await ref.read(nicknameProvider.notifier).setNickname(result);
                  }
                },
              ),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.lock),
              title: const Text('비밀번호 변경'),
              onTap: _showChangePasswordDialog,
            ),
            ListTile(
              leading: const Icon(Icons.logout),
              title: const Text('로그아웃'),
              onTap: _logout,
            ),
            ListTile(
              leading: const Icon(Icons.delete_forever, color: Colors.red),
              title: const Text('회원탈퇴', style: TextStyle(color: Colors.red)),
              onTap: _deleteAccount,
            ),
          ],
        ),
      ),
    );
  }
}
